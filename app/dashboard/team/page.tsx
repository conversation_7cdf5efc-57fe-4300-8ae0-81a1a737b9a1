import { getEmployees, getDepartments, getEmployeesFor<PERSON>anager } from "@/lib/data/index"
import { getCurrentUser, hasSuperAdminAccess } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RoleGuard } from "@/components/role-guard"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield, Star, Users, UserPlus } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { getEmployeeDepartmentDisplay, getManagerDepartmentSummary } from "@/lib/utils"
import Link from "next/link"
import type { Employee } from "@/lib/types"

export default async function TeamPage() {
  console.log('[TEAM PAGE] Loading team page')
  const user = await getCurrentUser()

  if (!user) {
    console.log('[TEAM PAGE] No user found, showing auth error')
    return (
      <div className="p-4 sm:p-6">
        <Alert variant="destructive">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Authentication required. Please sign in to access this page.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  console.log('[TEAM PAGE] User found:', user.fullName, 'Role:', user.role)

  return (
    <RoleGuard
      allowedRoles={['super-admin', 'manager', 'senior-manager', 'hr-admin']}
      userRole={user.role}
      redirectTo="/dashboard"
    >
      <TeamContent />
    </RoleGuard>
  )
}

async function TeamContent() {
  const currentUser = await getCurrentUser()

  if (!currentUser) {
    console.log('[TEAM PAGE] No current user found')
    return null
  }

  console.log('[TEAM PAGE] Loading team content for user:', currentUser.fullName, 'Role:', currentUser.role)

  let employees: Employee[] = []
  let departments: any[] = []
  let isManagerView = false

  try {
    // Super-admins and HR-admins see all employees, managers and senior-managers see their hierarchical employees
    if (hasSuperAdminAccess(currentUser) || currentUser.role === 'hr-admin') {
      employees = await getEmployees()
      console.log('[TEAM PAGE] Admin view - Loaded', employees.length, 'employees')
    } else if (currentUser.role === 'manager' || currentUser.role === 'senior-manager') {
      employees = await getEmployeesForManager(currentUser.id)
      isManagerView = true
      console.log('[TEAM PAGE] Manager/Senior-Manager view - Loaded', employees.length, 'managed employees for', currentUser.fullName)
    }

    departments = await getDepartments()
    console.log('[TEAM PAGE] Loaded', departments.length, 'departments')
  } catch (error) {
    console.log('[TEAM PAGE] Error loading data:', error)
    employees = []
    departments = []
  }

  // Group employees by department
  const employeesByDepartment = employees.reduce((acc, employee) => {
    const deptName = getEmployeeDepartmentDisplay(employee)
    if (!acc[deptName]) {
      acc[deptName] = []
    }
    acc[deptName].push(employee)
    return acc
  }, {} as Record<string, Employee[]>)

  console.log('[TEAM PAGE] Grouped employees by department:', Object.keys(employeesByDepartment))

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          {isManagerView ? (
            <Users className="h-6 w-6 text-blue-500" />
          ) : (
            <Star className="h-6 w-6 text-yellow-500" />
          )}
          <h1 className="text-2xl font-bold tracking-tight">
            {isManagerView ? 'My Team' : 'Company Team'}
          </h1>
        </div>
        <p className="text-muted-foreground">
          {isManagerView
            ? `Overview of employees under your management. Showing ${employees.length} team members.`
            : 'Overview of all employees across the company. Admin view.'
          }
        </p>
      </div>

      {/* Summary Stats */}
      <div className="grid gap-4 md:grid-cols-3 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {isManagerView ? 'Team Members' : 'Total Employees'}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employees.length}</div>
            {isManagerView && (
              <p className="text-xs text-muted-foreground">
                Under your management
              </p>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {isManagerView ? 'Departments Covered' : 'Departments'}
            </CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isManagerView ? Object.keys(employeesByDepartment).length : departments.length}
            </div>
            {isManagerView && (
              <p className="text-xs text-muted-foreground">
                Departments in your team
              </p>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employees.filter(e => e.active).length}</div>
            <p className="text-xs text-muted-foreground">
              Currently active
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Employees by Department */}
      {employees.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">
              {isManagerView ? 'No team members assigned' : 'You don\'t have people rn, pls add them'}
            </h3>
            <p className="text-muted-foreground mb-6">
              {isManagerView
                ? 'No employees are currently assigned to your management. Contact HR to assign team members.'
                : 'Get started by adding employees to your company to see the team overview.'
              }
            </p>
            {!isManagerView && (
              <Button asChild>
                <Link href="/dashboard/add-people">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add People
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {Object.entries(employeesByDepartment).map(([departmentName, deptEmployees]) => (
            <Card key={departmentName}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  {departmentName}
                  <Badge variant="secondary" className="ml-auto">
                    {deptEmployees.length} employees
                  </Badge>
                </CardTitle>
                <CardDescription>
                  {isManagerView
                    ? `Team members in the ${departmentName} department`
                    : `All employees in the ${departmentName} department`
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                  {deptEmployees.map((employee) => (
                    <Link
                      key={employee.id}
                      href={`/dashboard/employees/${employee.id}/profile`}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex flex-col">
                        <span className="font-medium text-blue-600 hover:text-blue-800">
                          {employee.fullName}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {employee.rate === 'hourly' ? 'Hourly Employee' : 'Monthly Employee'}
                        </span>
                        {isManagerView && employee.managers && employee.managers.length > 0 && (
                          <span className="text-xs text-muted-foreground">
                            Reports to: {getManagerDepartmentSummary(employee.managers)}
                          </span>
                        )}
                        {isManagerView && !employee.managers?.length && employee.managerName && employee.managerName !== currentUser.fullName && (
                          <span className="text-xs text-muted-foreground">
                            Reports to: {employee.managerName}
                          </span>
                        )}
                      </div>
                      <Badge
                        variant={employee.active ? "default" : "secondary"}
                        className={employee.active ? "bg-green-100 text-green-800" : ""}
                      >
                        {employee.active ? "Active" : "Inactive"}
                      </Badge>
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
